import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';

class CompleteYourProfileScreen extends StatefulWidget {
  const CompleteYourProfileScreen({super.key});

  @override
  State<CompleteYourProfileScreen> createState() =>
      _CompleteYourProfileScreenState();
}

class _CompleteYourProfileScreenState extends State<CompleteYourProfileScreen> {
  final countryPicker = const FlCountryCodePicker(
    title: SizedBox.shrink(),
  );
  TextEditingController genderC = TextEditingController();
  TextEditingController occupationC = TextEditingController();
  TextEditingController addressC = TextEditingController();
  TextEditingController dobC = TextEditingController();

  FocusNode genderF = FocusNode();
  FocusNode occupationF = FocusNode();
  FocusNode addressF = FocusNode();
  FocusNode dobF = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    genderC.dispose();
    occupationC.dispose();
    addressC.dispose();
    dobC.dispose();

    genderF.dispose();
    occupationF.dispose();
    addressF.dispose();
    dobF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Complete Your Profile',
            subtitle: "Please enter the information below",
          ),
          const YBox(40),
          CustomTextField(
            controller: genderC,
            focusNode: genderF,
            labelText: 'Gender',
            showLabelHeader: true,
            isReadOnly: true,
            suffixIcon: const Icon(
              Iconsax.arrow_down_1,
            ),
            onTap: () async {
              final r = await ModalWrapper.bottomSheet(
                context: context,
                widget: const SelectionModal(
                    selections: ['Male', 'Female', 'Other']),
              );

              if (r is String && context.mounted) {
                setState(() {
                  genderC.text = r;
                });
              }
            },
          ),
          const YBox(20),
          CustomTextField(
            controller: occupationC,
            focusNode: occupationF,
            labelText: 'Occupation',
            showLabelHeader: true,
          ),
          const YBox(20),
          CustomTextField(
            controller: addressC,
            focusNode: addressF,
            labelText: 'Address',
            showLabelHeader: true,
          ),
          const YBox(20),
          CustomTextField(
            controller: dobC,
            focusNode: dobF,
            labelText: 'Date of birth',
            showLabelHeader: true,
            isReadOnly: true,
            suffixIcon: const Icon(
              Iconsax.calendar_1,
            ),
            onTap: () {
              CustomCupertinoDatePicker(
                context: context,
                minimumDate: DateTime(1900, 1, 1),
                maximumDate: DateTime.now(),
                onDateTimeChanged: (dateTime) {
                  dobC.text = AppUtils.dayWithSuffixMonthAndYear(dateTime);
                  setState(() {});
                },
              ).show();
            },
          ),
          const YBox(60),
          CustomBtn.solid(
            text: 'Continue',
            onTap: () {
              Navigator.pushNamed(context, RoutePath.verifyYourIdentityScreen);
            },
          ),
        ],
      ),
    );
  }
}
