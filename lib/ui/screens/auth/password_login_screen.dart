import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class PasswordLoginScreen extends ConsumerStatefulWidget {
  const PasswordLoginScreen({super.key});

  @override
  ConsumerState<PasswordLoginScreen> createState() =>
      _PasswordLoginScreenState();
}

class _PasswordLoginScreenState extends ConsumerState<PasswordLoginScreen> {
  TextEditingController emailC =
      TextEditingController(text: '<EMAIL>');
  TextEditingController passwordC = TextEditingController(text: 'Boss@123');
  FocusNode emailF = FocusNode();
  FocusNode passwordF = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    emailC.dispose();
    passwordC.dispose();
    emailF.dispose();
    passwordF.dispose();

    super.dispose();
  }

  bool get passwordIsValid => passwordC.text.length >= 6;
  bool get emailIsValid =>
      emailC.text.contains('@') && emailC.text.contains('.');
  bool get isFormValid => emailIsValid && passwordIsValid;

  @override
  Widget build(BuildContext context) {
    return BusyOverlay(
      show: ref.watch(loginVm).isBusy,
      child: Scaffold(
        backgroundColor: AppColors.purpleF1,
        appBar: const CustomHeader(),
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          children: [
            const YBox(20),
            const CustomSubHeader(
              title: 'Good Day',
              subtitle:
                  "Welcome back! Please login into your \nEqualcash account to continue",
            ),
            const YBox(80),
            CustomTextField(
              controller: emailC,
              focusNode: emailF,
              labelText: 'Email',
              showLabelHeader: true,
              errorText: emailC.text.isNotEmpty && !emailIsValid
                  ? 'Please enter a valid email'
                  : null,
              onChanged: (p0) => setState(() {}),
              onSubmitted: (p0) {
                emailF.unfocus();
                passwordF.requestFocus();
              },
            ),
            const YBox(26),
            CustomTextField(
              controller: passwordC,
              focusNode: passwordF,
              labelText: 'Password',
              showLabelHeader: true,
              isPassword: true,
              errorText: passwordC.text.isNotEmpty && !passwordIsValid
                  ? 'Password must be at least 6 characters'
                  : null,
              onChanged: (p0) => setState(() {}),
              onSubmitted: (p0) {
                passwordF.unfocus();
              },
            ),
            const YBox(12),
            Align(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.forgotPasswordScreen);
                },
                child: Text(
                  'Forgot Password?',
                  style: AppTypography.text16.copyWith(
                    color: AppColors.primaryPurple,
                    height: 1.6,
                  ),
                ),
              ),
            ),
            const YBox(56),
            CustomBtn.solid(
              text: 'Login',
              online: isFormValid,
              onTap: () async {
                FocusManager.instance.primaryFocus?.unfocus();
                final loginRef = ref.read(loginVm);
                final r = await loginRef.login(
                  userName: emailC.text,
                  pwd: passwordC.text,
                );

                handleApiResponse(
                  response: r,
                  duration: 2,
                  onCompleted: () {
                    Navigator.pushNamedAndRemoveUntil(
                      context,
                      RoutePath.dashboardNavigationScreen,
                      (route) => false,
                    );
                  },
                );
              },
            ),
            const YBox(32),
            Align(
              alignment: Alignment.center,
              child: Text(
                'Don’t have an account?',
                style: AppTypography.text16.copyWith(
                  color: AppColors.neutral200,
                ),
              ),
            ),
            const YBox(12),
            Align(
              alignment: Alignment.center,
              child: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.createAccountScreen);
                },
                child: Text(
                  'Create Account',
                  style: AppTypography.text16.copyWith(
                    color: AppColors.primaryPurple,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const YBox(40),
          ],
        ),
      ),
    );
  }
}
