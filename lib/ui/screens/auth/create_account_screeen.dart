import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/gestures.dart';

class CreateAccountScreeen extends ConsumerStatefulWidget {
  const CreateAccountScreeen({super.key});

  @override
  ConsumerState<CreateAccountScreeen> createState() =>
      _CreateAccountScreeenState();
}

class _CreateAccountScreeenState extends ConsumerState<CreateAccountScreeen> {
  @override
  void initState() {
    super.initState();
    final createVm = ref.read(createAccountVm);
    KeyboardOverlay.addRemoveFocusNode(context, createVm.phoneF);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      createVm.setSelectedCountry(createVm.selectedCountry);
    });
  }

  @override
  Widget build(BuildContext context) {
    final createVm = ref.watch(createAccountVm);
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(10),
          const CustomSubHeader(
            title: 'Create Account',
            subtitle: "Please create an account for you to continue",
          ),
          const YBox(56),
          CustomTextField(
            labelText: 'Select Country',
            showLabelHeader: true,
            isReadOnly: true,
            prefixIcon: Row(
              children: [
                const XBox(16),
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: createVm.selectedCountry.flagImage(),
                ),
                const XBox(8),
                Text(
                  createVm.selectedCountry.name,
                  style: AppTypography.text16.copyWith(
                    color: AppColors.neutral300,
                  ),
                ),
              ],
            ),
            suffixIcon: const Icon(
              Iconsax.arrow_down_1,
              size: 24,
              color: AppColors.neutral300,
            ),
            onTap: () async {
              final createVm = ref.read(createAccountVm);
              final CountryCode? country =
                  await createVm.countryPicker.showPicker(
                context: context,
                pickerMaxHeight: Sizer.screenHeight * 0.8,
              );

              if (country != null) {
                createVm.setSelectedCountry(country);
              }
            },
          ),
          const YBox(40),
          Text(
            'Phone Number',
            style: TextStyle(
              color: AppColors.neutral400,
              fontSize: Sizer.text(16),
              fontWeight: FontWeight.w400,
            ),
          ),
          const YBox(4),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: CustomTextField(
                  isReadOnly: true,
                  controller: createVm.dialCodeC,
                ),
              ),
              const XBox(10),
              Expanded(
                flex: 8,
                child: CustomTextField(
                  controller: createVm.phoneC,
                  focusNode: createVm.phoneF,
                  hintText: 'Phone number',
                  keyboardType: KeyboardType.phone,
                  errorText: createVm.phoneF.hasFocus &&
                          createVm.phoneC.text.length < 10
                      ? 'Please enter a valid phone number'
                      : null,
                  onChanged: (p0) => createVm.reBuildUI(),
                ),
              ),
            ],
          ),
          const YBox(40),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(top: Sizer.height(2)),
                child: CustomCheckbox(
                  isSelected: createVm.termsIsSelected,
                  onTap: () {
                    ref
                        .read(createAccountVm)
                        .setTermsIsSelected(!createVm.termsIsSelected);
                  },
                ),
              ),
              const XBox(8),
              Expanded(
                child: RichText(
                    text: TextSpan(
                  children: [
                    TextSpan(
                      text: "By checking this box, you agree to our ",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.neutral200,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Nohemi',
                        height: 1.6,
                      ),
                    ),
                    TextSpan(
                      text: "\nTerms of service",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.primaryPurple,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Nohemi',
                        height: 1.6,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.customWebviewScreen,
                            arguments: WebViewArg(
                              webURL: AppConst.termUse,
                            ),
                          );
                        },
                    ),
                    TextSpan(
                      text: " and ",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.neutral200,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Nohemi',
                        height: 1.6,
                      ),
                    ),
                    TextSpan(
                      text: "Privacy Policy",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.primaryPurple,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Nohemi',
                        height: 1.6,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.customWebviewScreen,
                            arguments: WebViewArg(
                              webURL: AppConst.policy,
                            ),
                          );
                        },
                    ),
                    TextSpan(
                      text: " \nincluding verification of your identity ",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.neutral200,
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Nohemi',
                        height: 1.6,
                      ),
                    ),
                  ],
                )),
              ),
            ],
          ),
          const YBox(50),
          CustomBtn.solid(
            text: 'Continue',
            online: createVm.phoneC.text.length == 10,
            onTap: () {
              Navigator.pushNamed(context, RoutePath.verifyNumberOtpScreen);
              // if (_termsIsSelected) {
              //   ModalWrapper.showAlertDialog(
              //     context,
              //     title: 'We need to verify your number',
              //     content:
              //         'We need to make sure that +1656544321 is your number.',
              //     leftBtnText: 'Cancel',
              //     rightBtnText: 'OK',
              //     rightBtnOnTap: () {
              //       Navigator.pop(context);
              //       Navigator.pushNamed(
              //           context, RoutePath.verifyNumberOtpScreen);
              //     },
              //   );
              // }
            },
          ),
          const YBox(24),
          RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "Already have an account? ",
                    style: AppTypography.text16.copyWith(
                      color: AppColors.neutral200,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Nohemi',
                      height: 1.6,
                    ),
                  ),
                  TextSpan(
                    text: "Login",
                    style: AppTypography.text16.copyWith(
                      color: AppColors.primaryPurple,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'Nohemi',
                      height: 1.6,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.pushNamed(context, RoutePath.loginScreen);
                      },
                  ),
                ],
              )),
          const YBox(40),
        ],
      ),
    );
  }
}
