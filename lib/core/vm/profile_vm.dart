import 'package:dio/dio.dart';
import 'package:equalcash/core/core.dart';

class ProfileVm extends BaseVm {
  String? oldPin;
  String? newPin;
  String? _pinConfirm;
  String? get pinConfirm => _pinConfirm;
  set pinConfirm(String? value) {
    _pinConfirm = value;
    notifyListeners();
  }

  AuthUser? _authUser;
  AuthUser? get authUser => _authUser;
  String get fullName => "${_authUser?.firstName} ${_authUser?.lastName}";
  String? get profilePicture => _authUser?.profileImageUrl;
  bool get canChangePin =>
      (pinConfirm == newPin) &&
      (oldPin != newPin) &&
      (pinConfirm?.isNotEmpty ?? false);

  // Future<bool> loadUserFromStorage() async {
  //   final user = await StorageService.getUser();
  //   if (user != null) {
  //     _authUser = user;
  //     reBuildUI();
  //     return true;
  //   }
  //   return false;
  // }

  Future<ApiResponse> getUser() async {
    return await performApiCall(
      url: "/v1/auth/user",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        final result = authUserFromJson(json.encode(data["data"]));
        _authUser = result;
        // StorageService.storeUser(result);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> createPin({
    String? pin,
    String? pinConfirm,
    bool showLoader = false,
  }) async {
    return await performApiCall(
      url: "/v1/auth/transaction-pin/create",
      method: apiService.postWithAuth,
      body: {
        "pin": newPin ?? pin,
        "pin_confirmation": this.pinConfirm ?? pinConfirm,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> _changePin({
    String? oldPin,
    String? pin,
    String? pinConfirm,
    bool showLoader = false,
  }) async {
    if (showLoader) {
      setBusy(true);
    }
    return await performApiCall(
      url: "/v1/auth/transaction-pin/change",
      method: apiService.postWithAuth,
      body: {
        "old_pin": this.oldPin ?? oldPin,
        "pin": newPin ?? pin,
        "pin_confirmation": this.pinConfirm ?? pinConfirm,
      },
      onSuccess: (data) {
        if (showLoader) {
          setBusy(false);
        }
        return apiResponse;
      },
      onError: (error) {
        if (showLoader) {
          setBusy(false);
        }
        return apiResponse;
      },
    );
  }

  Future<ApiResponse<dynamic>> proceedToPinChange(
      [bool showLoader = false]) async {
    // Check if user has transaction pin
    ApiResponse<dynamic> res;
    if (authUser?.hasTransactionPin ?? false) {
      // User has transaction pin, proceed to change pin
      res = await _changePin(showLoader: showLoader);
    } else {
      // User does not have transaction pin, proceed to create pin
      res = await createPin(showLoader: showLoader);
    }
    clearPinData();
    return res;
  }

  Future<ApiResponse> confirmPin(String pin) async {
    return await performApiCall(
      url: "/v1/auth/transaction-pin/confirm",
      method: apiService.postWithAuth,
      isFormData: true,
      body: {"pin": pin},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> changePassword({
    required String? oldPassword,
    required String? newPassword,
    required String? passwordConfirm,
    bool showLoader = false,
  }) async {
    if (showLoader) {
      setBusy(true);
    }
    return await performApiCall(
      url: "/v1/auth/password/change",
      method: apiService.postWithAuth,
      body: {
        "current_password": oldPassword,
        "password": newPassword,
        "password_confirmation": passwordConfirm,
      },
      onSuccess: (data) {
        if (showLoader) {
          setBusy(false);
        }
        return apiResponse;
      },
      onError: (error) {
        if (showLoader) {
          setBusy(false);
        }
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> resetPassword({
    required String? code,
    required String? password,
    required String? passwordConfirm,
    required String? recipient,
    bool showLoader = false,
  }) async {
    if (showLoader) {
      setBusy(true);
    }
    return await performApiCall(
      url: "/v1/auth/password/reset",
      method: apiService.postWithAuth,
      body: {
        "code": code,
        "password": password,
        "password_confirmation": passwordConfirm,
        "recipient": recipient,
      },
      onSuccess: (data) {
        if (showLoader) {
          setBusy(false);
        }
        return apiResponse;
      },
      onError: (error) {
        if (showLoader) {
          setBusy(false);
        }
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> updateProfilePicture({
    required File image,
  }) async {
    return await performApiCall(
      url: "/v1/auth/profile/picture",
      method: apiService.postWithAuth,
      isFormData: true,
      body: {"image": await MultipartFile.fromFile(image.path)},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  void clearPinData() {
    oldPin = null;
    newPin = null;
    pinConfirm = null;
  }
}

final profileVm = ChangeNotifierProvider<ProfileVm>((ref) {
  return ProfileVm();
});
